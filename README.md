# 智能记账微信小程序原型

## 项目概述

这是一款功能完整的记账类微信小程序原型，采用现代化的UI设计和用户体验，帮助用户轻松管理个人财务。

## 功能特性

### 🏠 首页
- **今日支出概览** - 实时显示当日消费情况
- **本月统计** - 收入、支出、结余一目了然
- **快速记账** - 一键进入收入/支出记录
- **最近记录** - 显示最新的5条记账记录

### ➕ 记账页面
- **收支切换** - 支持收入和支出两种类型
- **分类选择** - 8种支出分类，5种收入分类
- **金额输入** - 支持小数点精确记录
- **备注功能** - 可添加详细说明
- **时间选择** - 自定义记录时间

### 📋 账单页面
- **月份切换** - 左右滑动查看不同月份
- **月度统计** - 显示当月收支汇总
- **按日分组** - 账单按日期分组显示
- **详细信息** - 每条记录显示分类、时间、备注

### 📊 统计页面
- **时间范围** - 支持本周、本月、本年统计
- **数据可视化** - 图表展示支出趋势
- **分类排行** - Top5支出分类排名
- **百分比分析** - 各分类占比统计

### ⚙️ 设置页面
- **用户信息** - 个人资料展示
- **分类管理** - 自定义收支分类
- **提醒设置** - 记账提醒开关
- **数据导出** - 支持JSON格式导出
- **隐私设置** - 数据安全保护

## 技术特点

### 🎨 UI/UX设计
- **TailwindCSS** - 现代化CSS框架，响应式设计
- **Unsplash图片** - 高质量背景图片
- **FontAwesome图标** - 丰富的图标库
- **渐变色彩** - 温馨的绿色主题
- **圆角设计** - 现代化的视觉风格

### 💾 数据管理
- **LocalStorage** - 本地数据持久化
- **JSON格式** - 结构化数据存储
- **实时更新** - 数据变化即时反映
- **数据导出** - 支持备份功能

### 📱 交互体验
- **单页应用** - 流畅的页面切换
- **底部导航** - 符合移动端习惯
- **快速操作** - 减少用户操作步骤
- **即时反馈** - 操作结果及时提示

## 分类系统

### 支出分类
- 🍽️ 餐饮 - 日常用餐消费
- 🚗 交通 - 出行交通费用
- 🛍️ 购物 - 日用品购买
- 🎮 娱乐 - 休闲娱乐消费
- ❤️ 医疗 - 医疗健康支出
- 🎓 教育 - 学习培训费用
- 🏠 住房 - 房租水电费用
- ⋯ 其他 - 其他类型支出

### 收入分类
- 💼 工资 - 固定工资收入
- 🎁 奖金 - 奖金津贴收入
- 📈 投资 - 投资理财收益
- ⏰ 兼职 - 兼职副业收入
- ⋯ 其他 - 其他类型收入

## 使用说明

### 快速开始
1. 打开 `index.html` 文件
2. 系统会自动加载演示数据
3. 点击底部导航切换页面
4. 开始体验记账功能

### 记账流程
1. 点击首页"快速记账"或底部"记账"按钮
2. 选择收入或支出类型
3. 输入金额和选择分类
4. 添加备注（可选）
5. 确认时间后保存记录

### 数据查看
1. 首页查看今日和本月概览
2. 账单页面查看详细记录
3. 统计页面分析消费趋势
4. 设置页面管理个人信息

## 演示数据

系统内置了以下演示数据：
- 今日餐饮支出：¥25.50
- 今日交通支出：¥12.00
- 昨日工资收入：¥5000.00
- 前日购物支出：¥89.90
- 三日前娱乐支出：¥35.00

## 浏览器兼容性

- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+
- ✅ 移动端浏览器

## 项目结构

```
demo/
├── index.html          # 主页面文件
├── README.md          # 项目说明文档
└── 其他文件...
```

## 开发说明

### 核心技术栈
- HTML5 + CSS3 + JavaScript
- TailwindCSS 3.x
- FontAwesome 6.x
- Chart.js (图表功能)

### 数据结构
```javascript
{
  id: 时间戳,
  type: 'expense' | 'income',
  amount: 金额,
  category: '分类名称',
  note: '备注信息',
  datetime: '2024-01-01T12:00',
  date: '2024-01-01'
}
```

## 未来扩展

- [ ] 预算管理功能
- [ ] 账户管理（多账户支持）
- [ ] 数据同步（云端备份）
- [ ] 图表可视化增强
- [ ] 导入导出功能完善
- [ ] 多语言支持
- [ ] 主题切换功能

## 联系方式

如有问题或建议，欢迎反馈！

---

*本项目为微信小程序原型演示，展示了完整的记账应用功能和用户体验设计。*
